import SpeechSynthesizer from "/utils/tts"
import { sleep, getRandomString } from "/utils"
import { useStore } from "/store"
import ttsMonitor from "/utils/ttsMonitor"

const store = useStore()
const fs = wx.getFileSystemManager()

let tts
let audioCtx
let queue = []
let handleCount = 0
let fileQueue = []
let allDoneFlag = false
let changeCallback, onVoiceStatusChangeCallback
let voiceStatus = ""

// 配置常量
const CONFIG = {
  PLAY_TIMEOUT: 5000, // 增加播放超时时间到5秒
  MAX_RETRY_COUNT: 3, // 最大重试次数
  RETRY_DELAY: 1000, // 重试延迟
  FILE_CLEANUP_DELAY: 100, // 文件清理延迟
}

// 性能监控
const performanceMonitor = {
  playStartTime: 0,
  totalPlayTime: 0,
  errorCount: 0,
  retryCount: 0,

  startPlay() {
    this.playStartTime = Date.now()
  },

  endPlay() {
    if (this.playStartTime) {
      this.totalPlayTime += Date.now() - this.playStartTime
      this.playStartTime = 0
    }
  },

  recordError() {
    this.errorCount++
    console.warn(`[TTS] 播放错误计数: ${this.errorCount}`)
  },

  recordRetry() {
    this.retryCount++
    console.warn(`[TTS] 重试计数: ${this.retryCount}`)
  },

  getStats() {
    return {
      totalPlayTime: this.totalPlayTime,
      errorCount: this.errorCount,
      retryCount: this.retryCount,
    }
  }
}

const handleFileQueue = async (currentFileQueue) => {
  if (!currentFileQueue || currentFileQueue.length === 0) {
    console.log("[TTS] 文件队列为空，结束播放")
    if (allDoneFlag && voiceStatus !== "complete") {
      voiceStatus = "complete"
      onVoiceStatusChangeCallback?.("complete")
    }
    return
  }

  const file = currentFileQueue.shift()
  console.log(`[TTS] 开始处理文件: ${file}, 剩余队列长度: ${currentFileQueue.length}`)

  try {
    await playFileWithRetry(file)

    // 检查是否所有文件都播放完成
    if (currentFileQueue.length === 0 && allDoneFlag && voiceStatus !== "complete") {
      voiceStatus = "complete"
      onVoiceStatusChangeCallback?.("complete")
      console.log("[TTS] 所有文件播放完成")
    } else if (currentFileQueue.length > 0) {
      // 继续播放下一个文件
      await handleFileQueue(currentFileQueue)
    }
  } catch (err) {
    console.error(`[TTS] 文件播放失败: ${file}`, err)
    performanceMonitor.recordError()

    // 如果还有其他文件，继续播放
    if (currentFileQueue.length > 0) {
      await handleFileQueue(currentFileQueue)
    } else {
      // 所有文件都播放失败
      voiceStatus = "error"
      onVoiceStatusChangeCallback?.("error")
    }
  }
}

// 带重试的文件播放
const playFileWithRetry = async (file, retryCount = 0) => {
  try {
    await playFile(file)
  } catch (error) {
    if (retryCount < CONFIG.MAX_RETRY_COUNT) {
      console.warn(`[TTS] 播放失败，进行第${retryCount + 1}次重试: ${file}`)
      performanceMonitor.recordRetry()
      await sleep(CONFIG.RETRY_DELAY)
      return playFileWithRetry(file, retryCount + 1)
    } else {
      throw error
    }
  }
}

const playFile = (file) => {
  return new Promise((resolve, reject) => {
    // 开始监控会话
    const sessionId = ttsMonitor.startPlaySession(file)

    // 清理之前的音频上下文
    if (audioCtx) {
      try {
        audioCtx.destroy()
      } catch (e) {
        console.warn("[TTS] 清理旧音频上下文失败:", e)
        ttsMonitor.recordError('cleanup', `清理音频上下文失败: ${e.message}`, sessionId)
      }
    }

    audioCtx = wx.createInnerAudioContext({
      useWebAudioImplement: true,
    })

    audioCtx.src = file
    audioCtx.autoplay = voiceStatus !== "pause"

    console.log(`[TTS] 创建音频上下文，文件: ${file}, 自动播放: ${audioCtx.autoplay}`)
    performanceMonitor.startPlay()

    // 增加超时时间并添加更详细的日志
    const timeoutId = setTimeout(() => {
      console.error(`[TTS] 播放超时 (${CONFIG.PLAY_TIMEOUT}ms): ${file}`)
      performanceMonitor.recordError()
      ttsMonitor.recordError('timeout', `播放超时: ${file}`, sessionId)
      ttsMonitor.endPlaySession(sessionId, 'timeout')
      cleanupAudio(file, false) // 不删除文件，可能需要重试
      reject(new Error(`播放超时: ${file}`))
    }, CONFIG.PLAY_TIMEOUT)

    const updateStatus = (status) => {
      voiceStatus = status
      onVoiceStatusChangeCallback?.(status)
      console.log(`[TTS] 状态更新: ${status}`)
    }

    // 播放开始
    audioCtx.onPlay(() => {
      clearTimeout(timeoutId)
      console.log(`[TTS] 开始播放: ${file}`)
      updateStatus("play")
    })

    // 播放结束
    audioCtx.onEnded(() => {
      clearTimeout(timeoutId)
      console.log(`[TTS] 播放结束: ${file}`)
      performanceMonitor.endPlay()
      ttsMonitor.endPlaySession(sessionId, 'completed')
      cleanupAudio(file, true) // 播放完成，删除文件
      resolve()
    })

    // 播放错误
    audioCtx.onError((res) => {
      clearTimeout(timeoutId)
      console.error(`[TTS] 播放错误: ${file}`, res)
      performanceMonitor.recordError()
      ttsMonitor.recordError('play', `播放错误: ${JSON.stringify(res)}`, sessionId)
      ttsMonitor.endPlaySession(sessionId, 'error')
      cleanupAudio(file, false) // 不删除文件，可能需要重试
      reject(new Error(`播放错误: ${JSON.stringify(res)}`))
    })

    // 播放停止
    audioCtx.onStop(() => {
      clearTimeout(timeoutId)
      console.log(`[TTS] 停止播放: ${file}`)
      updateStatus("stop")
      cleanupAudio(file, true) // 主动停止，删除文件
      resolve() // 主动停止也算完成
    })

    // 播放暂停
    audioCtx.onPause(() => {
      console.log(`[TTS] 暂停播放: ${file}`)
      updateStatus("pause")
    })

    // 监听加载完成
    audioCtx.onCanplay(() => {
      console.log(`[TTS] 音频可以播放: ${file}`)
    })
  })
}

const cleanupAudio = (file, shouldDeleteFile = true) => {
  try {
    // 销毁音频上下文
    if (audioCtx) {
      audioCtx.destroy()
      audioCtx = null
      console.log(`[TTS] 音频上下文已销毁: ${file}`)
    }

    // 根据参数决定是否删除文件
    if (shouldDeleteFile && file) {
      // 延迟删除文件，确保音频播放完全结束
      setTimeout(() => {
        fs.unlink({
          filePath: file,
          success: () => {
            console.log(`[TTS] 文件删除成功: ${file}`)
          },
          fail: (err) => {
            console.error(`[TTS] 文件删除失败: ${file}`, err)
          },
        })
      }, CONFIG.FILE_CLEANUP_DELAY)
    }
  } catch (error) {
    console.error(`[TTS] 清理资源失败: ${file}`, error)
  }
}

// 清理所有文件队列
const cleanupAllFiles = async (filesToClean = []) => {
  console.log(`[TTS] 开始清理 ${filesToClean.length} 个文件`)

  const cleanupPromises = filesToClean.map(
    (file) =>
      new Promise((resolve) => {
        fs.unlink({
          filePath: file,
          complete: (res) => {
            if (res.errMsg && !res.errMsg.includes('ok')) {
              console.warn(`[TTS] 清理文件失败: ${file}`, res)
            } else {
              console.log(`[TTS] 清理文件成功: ${file}`)
            }
            resolve()
          },
        })
      })
  )

  try {
    await Promise.all(cleanupPromises)
    console.log(`[TTS] 所有文件清理完成`)
  } catch (error) {
    console.error(`[TTS] 批量清理文件时出错:`, error)
  }
}

const handleQueue = (_handleCount) => {
  const dataArr = queue[_handleCount]

  if (!dataArr?.length) {
    console.log(`[TTS] 队列 ${_handleCount} 数据为空，跳过处理`)
    return
  }

  const filePath = `${wx.env.USER_DATA_PATH}/${getRandomString(32)}.mp3`
  console.log(`[TTS] 处理队列 ${_handleCount}, 数据段数量: ${dataArr.length}, 文件路径: ${filePath}`)

  // 记录队列处理
  ttsMonitor.recordQueueProcess(dataArr.length)

  fs.open({
    filePath,
    flag: "a+",
    success: (res) => {
      try {
        dataArr.forEach((data, index) => {
          fs.appendFileSync(filePath, data, "binary")

          if (index === dataArr.length - 1) {
            fs.close({
              fd: res.fd,
              success: () => {
                console.log(`[TTS] 文件创建成功: ${filePath}`)
                ttsMonitor.recordFileOperation('create', true)
                fileQueue.push(filePath)

                // 只有第一个文件时开始播放
                if (_handleCount === 0) {
                  console.log(`[TTS] 开始播放队列，文件数量: ${fileQueue.length}`)
                  handleFileQueue([...fileQueue]) // 传递队列副本
                  voiceStatus = "start"
                  onVoiceStatusChangeCallback?.("start")
                }
              },
              fail: (err) => {
                console.error(`[TTS] 关闭文件失败: ${filePath}`, err)
                // 清理失败的文件
                fs.unlink({
                  filePath,
                  fail: (unlinkErr) => console.error(`[TTS] 清理失败文件出错: ${filePath}`, unlinkErr)
                })
              },
            })
          }
        })
      } catch (error) {
        console.error(`[TTS] 写入文件数据失败: ${filePath}`, error)
        fs.close({
          fd: res.fd,
          fail: (closeErr) => console.error(`[TTS] 关闭失败文件描述符出错`, closeErr)
        })
      }
    },
    fail: (err) => {
      console.error(`[TTS] 打开文件失败: ${filePath}`, err)
    },
  })
}

const init = async () => {
  if (!store.nlsToken) {
    await store.fetchNlsToken()
  }

  tts = new SpeechSynthesizer({
    url: store.nlsUrl,
    appkey: store.nlsAppKey,
    token: store.nlsToken,
  })

  const setupEventHandler = (event, handler) => {
    tts.on(event, handler)
  }

  setupEventHandler("data", (msg) => {
    // console.log("Client recv data:", JSON.stringify(msg))
    if (!queue[queue.length - 1]) {
      queue[queue.length - 1] = []
    }
    queue[queue.length - 1].push(msg)
  })

  setupEventHandler("started", (msg) => {
    console.log("Client recv started", msg)
    changeCallback?.("started", JSON.parse(msg))
  })

  setupEventHandler("completed", async (msg) => {
    console.log("Client recv completed:", msg)
    allDoneFlag = true
    changeCallback?.("completed", JSON.parse(msg))
  })

  setupEventHandler("begin", (msg) => {
    console.log("Client recv begin:", msg)
    queue.push([])
    changeCallback?.("begin", JSON.parse(msg))
  })

  setupEventHandler("end", (msg) => {
    console.log("Client recv end:", msg)
    changeCallback?.("end", JSON.parse(msg))
    handleQueue(handleCount)
    handleCount++
  })

  setupEventHandler("closed", () => {
    console.log("Client recv closed")
    changeCallback?.("closed")
  })

  setupEventHandler("failed", (msg) => {
    console.log("Client recv failed:", msg)
    allDoneFlag = true
    changeCallback?.("failed", JSON.parse(msg))
  })
}

export const useTTS = () => {
  init()

  const startTTS = async () => {
    console.log("[TTS] 开始启动TTS")

    if (!tts) {
      console.log("[TTS] TTS未初始化，开始初始化")
      await init()
    }

    // 重置所有状态
    queue = []
    handleCount = 0
    fileQueue = []
    allDoneFlag = false
    voiceStatus = ""

    // 重置性能监控
    performanceMonitor.playStartTime = 0
    performanceMonitor.totalPlayTime = 0
    performanceMonitor.errorCount = 0
    performanceMonitor.retryCount = 0

    try {
      await tts.start(
        Object.assign(tts.defaultStartParams(), {
          voice: "zhiyuan",
          format: "mp3",
        })
      )
      console.log("[TTS] TTS启动成功")
    } catch (error) {
      console.error("[TTS] TTS启动失败:", error)
      throw error
    }
  }

  const sendTTSText = async (text) => {
    const cleanText = text.replace(/[#`*]/g, "")
    tts.send(cleanText)
  }

  const stopTTS = async (stopAudio = true) => {
    console.log(`[TTS] 停止TTS, stopAudio: ${stopAudio}`)

    if (stopAudio) {
      // 停止当前播放
      if (audioCtx) {
        try {
          audioCtx.stop()
        } catch (e) {
          console.warn("[TTS] 停止音频播放失败:", e)
        }
      }

      // 清理所有文件
      if (fileQueue.length > 0) {
        await cleanupAllFiles([...fileQueue])
        fileQueue = []
      }

      // 重置状态
      voiceStatus = "stop"
      onVoiceStatusChangeCallback?.("stop")
    }

    // 关闭TTS连接
    try {
      if (tts) {
        await tts.close()
        console.log("[TTS] TTS连接已关闭")
      }
    } catch (e) {
      console.error("[TTS] 关闭TTS连接失败:", e)
    }

    // 打印性能统计
    const stats = performanceMonitor.getStats()
    console.log("[TTS] 性能统计:", stats)
  }

  const onTTSChange = (callback) => {
    changeCallback = callback
  }

  const pauseTTS = () => {
    audioCtx?.pause()
  }

  const resumeTTS = () => {
    audioCtx?.play()
  }

  const onVoiceStatusChange = (callback) => {
    onVoiceStatusChangeCallback = callback
  }

  const getVoiceStatus = () => voiceStatus

  return {
    startTTS,
    sendTTSText,
    stopTTS,
    pauseTTS,
    resumeTTS,
    onTTSChange,
    onVoiceStatusChange,
    getVoiceStatus,
  }
}
