<script setup>
import MessageContent from "./MessageContent.vue"

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  isMute: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "chat",
  },
})

const emit = defineEmits(["manual", "rate", "refresh", "speak"])

const onCopy = (message) => {
  console.log("onCopy", message)
  const text = message.content
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: "已复制到剪贴板",
        icon: "none",
      })
    },
  })
}
const onSpeak = (message, index) => {
  console.log("onSpeak", message, index)
  emit("speak", message, index, props.mode)
}
const onRate = (message) => {
  console.log("onRate", message)
  emit("rate", message)
}
const onManual = (message) => {
  console.log("onManual", message)
  emit("manual")
}
const onRefresh = (message) => {
  console.log("onRefresh", message)
  emit("refresh")
}
</script>

<template>
  <view
    class="message"
    :class="message.role"
    :id="`message-${index}`"
    v-for="(message, index) in list"
    :key="message.id"
  >
    <view
      class="message-content"
      :class="message.role"
      v-if="message.role === 'user'"
    >
      {{ message.content }}
    </view>

    <template v-else-if="['assistant', 'artificial'].includes(message.role)">
      <view class="generating" v-if="message.status === 'generating'">
        <image
          class="generating"
          src="/static/images/generating.gif"
          mode="aspectFit"
        />
        <text>思考中...</text>
      </view>

      <MessageContent
        v-else
        :message="message"
        :mode="mode"
        :is-last="index === list.length - 1"
        @copy="() => onCopy(message)"
        @speak="() => onSpeak(message, index)"
        @rate="() => onRate(message)"
        @manual="() => onManual(message)"
        @refresh="() => onRefresh(message)"
      />
    </template>

    <view class="manual" v-else-if="message.role === 'manual'">
      <template v-if="message.status === 'entered'"
        >您已成功接入人工客服
      </template>
      <template v-else-if="message.status === 'exited'"
        >您已退出人工客服
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.message {
  padding: 12rpx 0;
  display: flex;

  &.user {
    padding-left: 42rpx;
    justify-content: flex-end;
  }
}

.message-content {
  font-size: 34rpx;
  line-height: 54rpx;
  padding: 30rpx;
  text-align: justify;

  &.user {
    background: #466fff;
    border-radius: 48rpx 48rpx 12rpx 48rpx;
    color: #ffffff;
    width: fit-content;
    max-width: 100%;
  }
}

.manual {
  width: 100%;
  font-size: 24rpx;
  padding: 20rpx;
  text-align: center;
  color: #6e7191;
}

.generating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #6e7191;

  image {
    width: 102rpx;
    height: 102rpx;
    margin-right: 24rpx;
  }
}
</style>
